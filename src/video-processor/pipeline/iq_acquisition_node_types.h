#pragma once

#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

// struct IQAcquisitionNodeConfig {
//   size_t iqReadBufferSize;
//
//   size_t writeChunkSize;      // Size of each write operation in samples
//   size_t readChunkSize;       // Size of each read window in samples
//   size_t readOverlapSize;     // Overlap between consecutive read windows in samples
//   size_t numOfWriteChunks;    // Number of write chunks that fit into the internal buffer
// };

struct RawOverlappedIQChunk {
  const SampleType* chunkBuffer;
  size_t chunkSize;
  size_t overlapSize;
};

} // namespace IQVideoProcessor::Pipeline
