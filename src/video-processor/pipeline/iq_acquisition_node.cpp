#include "iq_acquisition_node.h"
#include "../../configs.h"

namespace IQVideoProcessor::Pipeline {

constexpr size_t      IQ_STREAM_READ_SIZE = 8 * 1024;       // 8K samples for reading
                      // NTSC - 15734 lines/sec, PAL - 15625 lines/sec
constexpr double      MIN_LINE_RATE_HZ = 15000.0;           // Minimum line rate for video processing (auto)
constexpr double      LINES_PER_CHUNK = 5.0;                // Put 5 lines per chunk for processing


size_t calcOutputChunkSize(const SampleRateType sampleRate) {
  const auto samplesPerVideoLine = static_cast<double>(sampleRate) / MIN_LINE_RATE_HZ;
  return static_cast<size_t>(samplesPerVideoLine * LINES_PER_CHUNK);
}

size_t calcOutputOverlapSize(const SampleRateType sampleRate) {
  constexpr auto ratio = COMPOSITE_SIGNAL_SYNC_SEEK_FILTER_HZ / 2 + 1; // Should be more than 1/2 of the filter frequency
  const auto minimalOverlap = std::lround(sampleRate / ratio);
  return static_cast<size_t>(minimalOverlap) + 1; // Add 1 to ensure we have at least one sample overlap
}

IQAcquisitionNode::IQAcquisitionNode(std::unique_ptr<IIQStream> stream)
  : sampleRate_(0), iqStreamReadSize_(IQ_STREAM_READ_SIZE), iqOutputChunkSize_(0),
    iqOutputChunkOverlapSize_(0), iqStream_(std::move(stream)), samplesProcessed_(0) {

  if (!iqStream_) return; // just to nothing if stream is null

  sampleRate_ = iqStream_->sampleRate();
  iqStreamReadSize_ = IQ_STREAM_READ_SIZE;
  iqOutputChunkSize_ = calcOutputChunkSize(sampleRate_);
  iqOutputChunkOverlapSize_ = calcOutputOverlapSize(sampleRate_);

  if (iqOutputChunkSize_ == 0) return; // If chunk size is zero, we cannot proceed

  /*
   * Creating a chunk processor that generates chunks containing approximately {LINES_PER_CHUNK} video lines each,
   * with sufficient overlap to meet the sync filter requirements at {COMPOSITE_SIGNAL_SYNC_SEEK_FILTER_HZ} Hz.
  */
  chunkProcessor_ = std::make_unique<ChunkProcessor<SampleType>>(
    iqStreamReadSize_,
    iqOutputChunkSize_,
    iqOutputChunkOverlapSize_,
    16,
    ([this](const SampleType *buffer, const size_t x, const size_t y) {
      this->onOutputChunkReady(buffer, x, y);
    })
  );
  // Making the node running
  setRunning();
}

IQAcquisitionNode::~IQAcquisitionNode() {
  shutdown();
}

bool IQAcquisitionNode::process(bool &&input) {
  return false; // We don't process input directly in this node
}

bool IQAcquisitionNode::hasPendingWork() const {
  return false;
}

bool IQAcquisitionNode::tick() {
  if (!running()) {
    return false; // Node is not running
  }

  auto *buffer = chunkProcessor_->getWriteChunkPtr();
  if (!iqStream_->readSamples(buffer, iqStreamReadSize_)) {
    shutdown();
    return false;
  }

  if (!running()) {
    return false; // Node is not running
  }

  chunkProcessor_->commitWriteChunk();
  return true;
}

void IQAcquisitionNode::onOutputChunkReady(const SampleType *chunkBuffer, const size_t chunkSize, const size_t overlapSize) {
  if (!running()) return;

  RawOverlappedIQChunk output {
    .chunkBuffer = chunkBuffer,
    .chunkSize = chunkSize,
    .overlapSize = overlapSize
  };


  if (this->sendOutput(output)) {
    samplesProcessed_ += chunkSize - overlapSize; // Update processed samples count
  } else {
    // If output link rejected the data, we should stop processing
    shutdown();
  }
}

} // namespace IQVideoProcessor::Pipeline

