#pragma once

#include "./iq_acquisition_node_types.h"
#include "../../chunk-processor/chunk_processor.h"
#include "../../iiq-stream/iiq_stream.h"
#include "../../stream-pipeline/stream_node.h"
#include "../../types.h"

namespace IQVideoProcessor::Pipeline {

class IQAcquisitionNode final : public SPipeline::StreamNode<bool, RawOverlappedIQChunk&> {
public:
  explicit IQAcquisitionNode(std::unique_ptr<IIQStream> stream);
  ~IQAcquisitionNode() override;

  bool process(bool &&input) override;
  bool tick() override;
  [[nodiscard]] bool hasPendingWork() const override;

private:
  uint32_t sampleRate_;
  size_t iqStreamReadSize_;
  size_t iqOutputChunkSize_;
  size_t iqOutputChunkOverlapSize_;
  std::unique_ptr<IIQStream> iqStream_;
  std::unique_ptr<ChunkProcessor<SampleType>> chunkProcessor_;
  size_t samplesProcessed_; // Not sure why, but let it count

  void onOutputChunkReady(const SampleType *chunkBuffer, size_t chunkSize, size_t overlapSize);
};

} // namespace IQVideoProcessor::Pipeline
